/**
* @file frame-correct.jsx 检查模式弹窗
* @create 李爽@2022.09.26
*/
import { Layout, Image, Tag, Button, Space, Anchor, Tooltip, message } from 'antd';
import { EyeOutlined, PicLeftOutlined, PicRightOutlined } from '@ant-design/icons';
import LazyLoad from 'react-lazyload';

import styles from '../../../../../../../../assets/css/perf-assess/speed-analyse.module.css';

const { Content } = Layout;
const { Link } = Anchor;
const lazyLoadImg = '../../../../../../../../image/loading.png';

const frameDetail = (record, rowIndex, { state, dispatch }) => {
    let totalCount = record.frameList.length;
    let frameJsx = [];
    // 生成每一帧图片
    for (let index = 0; index < totalCount; index++) {
        if (index < 0) {
            continue;
        }
        let border = '1px solid';
        let maskMsg = '';
        let id = '';
        id = `stage_${index}`
        if (-1 !== state.editor.perfAssess.operator.speedAnalyse.stageList.indexOf(index)) {
            border = '3px dashed orange';
            maskMsg = '转场点';
        }
        if (index === record.smartFirstFrame) {
            border = '3px dashed #ff0000';
            maskMsg = '智能首帧';
            id = 'smart-first-frame';
        } else if (index === record.smartLastFrame) {
            border = '3px dashed #E8A20F';
            maskMsg = '智能尾帧';
            id = 'smart-last-frame';
        }
        if (index === record.manualFirstFrame) {
            border = '3px solid #ff0000';
            maskMsg = '人工首帧';
            id = 'manual-first-frame';
        }
        if (index === record.manualLastFrame) {
            border = '3px solid #E8A20F';
            maskMsg = '人工尾帧';
            id = 'manual-last-frame';
        }
        frameJsx.push(
            <Tooltip
                placement='top'
                title={
                    <div>
                        <div>index: {index}</div>
                        <div>{record.frameList[index].name}</div>
                    </div>
                }
            >
                <li
                    key={index}
                    className={styles.whole_frame_li}
                    style={{
                        width: state.editor.perfAssess.isPerfPlanCollapsed ? '14.285%' : '15.65%'
                    }}
                    id={'' !== id ? id : null}
                >
                    <div
                        style={{
                            position: 'absolute',
                            left: 0,
                            zIndex: 998,
                            width: border.slice(0, 4),
                            height: (state.editor.screen.height - 300) / 2,
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            right: 0,
                            zIndex: 998,
                            width: border.slice(0, 4),
                            height: (state.editor.screen.height - 300) / 2,
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            zIndex: 998,
                            width: '100%',
                            height: border.slice(0, 4),
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            bottom: 27,
                            zIndex: 998,
                            width: '100%',
                            height: border.slice(0, 4),
                            border
                        }}>
                    </div>
                    <LazyLoad
                        resize
                        scrollContainer={() => document.getElementById('correct-list')}
                        overflow
                        placeholder={
                            <img
                                style={{
                                    height: state.editor.screen.height / 3.5,
                                    width: (state.editor.screen.width - 270) / 12,
                                }}
                                src={lazyLoadImg}
                                alt='logo'
                            />
                        }
                    >
                        <Image
                            placeholder
                            width={'100%'}
                            height={(state.editor.screen.height - 300) / 2}
                            src={record.frameList[index].frame}
                            preview={{
                                mask: (
                                    <Space direction='vertical'>
                                        <Button
                                            shape='round'
                                            icon={<PicLeftOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                record.manualFirstFrame = index;
                                                record.manualFirstTimestamp = record.frameList[index].timestamp;
                                                record.manualFirstStatus = 1;
                                                let correctList =
                                                    state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                correctList[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        correctList
                                                    }
                                                });
                                                dispatch({
                                                    type: 'editor/speedFrameCorrect',
                                                    payload: {
                                                        recordId: record.recordId,
                                                        type: 'first',
                                                        frame: index,
                                                        timestamp: record.frameList[index].timestamp
                                                    }
                                                });
                                                message.success('首帧已设定');
                                            }}
                                        >
                                            首帧
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<EyeOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        previewIndex: index
                                                    }
                                                });
                                            }}
                                        >
                                            1
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<PicRightOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                record.manualLastFrame = index;
                                                record.manualLastTimestamp = record.frameList[index].timestamp;
                                                record.manualLastStatus = 1;
                                                let correctList =
                                                    state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                correctList[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        correctList
                                                    }
                                                });
                                                dispatch({
                                                    type: 'editor/speedFrameCorrect',
                                                    payload: {
                                                        recordId: record.recordId,
                                                        type: 'last',
                                                        frame: index,
                                                        timestamp: record.frameList[index].timestamp
                                                    }
                                                });
                                                message.success('尾帧已设定');
                                            }}
                                        >
                                            尾帧
                                        </Button>
                                    </Space>
                                ),
                                visible: state.editor.perfAssess.operator.speedAnalyse.previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                previewIndex: -1
                                            }
                                        });
                                    }
                                }
                            }}
                        />
                        {
                            '' !== maskMsg ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                        width: '100%',
                                        zIndex: 998,
                                        marginTop: -22,
                                        background: 'rgba(0, 0, 0, 0.5)',
                                        color: 'white',
                                        position: 'relative'
                                    }}
                                >
                                    {maskMsg}
                                </div>
                            ) : null
                        }

                    </LazyLoad>
                    <div
                        style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                    >
                        <Tag
                            color="orange"
                            style={{ padding: '0 8px' }}
                        >
                            {record.frameList[index].timestamp - record.frameList[0].timestamp} ms
                        </Tag>
                    </div>
                </li>
            </Tooltip>
        );
    }
    return frameJsx;
};

export default ({ dispatch, state, rowIndex, record }) => {
    setTimeout(() => {
        if (document.getElementById('manual-first-frame')) {
            document.getElementById('manual-first-frame').scrollIntoView({
                block: 'center',
                inline: 'center'
            });
        }
    }, 100);
    const getLinkJsx = () => {
        let jsx = [];
        let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
        for (let item of stageList) {
            jsx.push(<Link href={`#stage_${item}`} title={item} />);
        }
        return jsx;
    }
    return (
        <Content
            id={'correct-list'}
            style={{
                position: 'relative',
                width: '100%',
                overflow: 'auto',
                height: state.editor.screen.height - 330,
                marginTop: -10,
                backgroundColor: '#fff'
            }}
        >
            <ul
                className='ulImgs'
                style={{
                    listStyle: 'none',
                    height: '100%',
                    margin: 0,
                    marginLeft: 100,
                    padding: 0,
                    textAlign: 'center'
                }}
            >
                {
                    0 !== Object.keys(record).length && -1 !== Object.keys(record).indexOf('frameList') ?
                        frameDetail(record, rowIndex, { state, dispatch }) : null
                }
            </ul>
            {
                0 !== Object.keys(record).length && -1 !== Object.keys(record).indexOf('frameList') ?
                    <div style={{ width: 100 }}>
                        <h4 style={{ position: 'fixed', marginLeft: 10, top: 250, textAlign: 'center' }}>
                            重要节点导航 <br />
                        </h4>
                        <Anchor
                            targetOffset={(state.editor.screen.height - 250) / 2}
                            getContainer={() => document.getElementById('correct-list')}
                            style={{
                                position: 'fixed',
                                marginLeft: 10,
                                top: 280
                            }}
                        >
                            <Link href={'#smart-first-frame'} title='智能首帧' />
                            <Link href={'#smart-last-frame'} title='智能尾帧' />
                            <Link href={'#manual-first-frame'} title='人工首帧' />
                            <Link href={'#manual-last-frame'} title='人工尾帧' />
                        </Anchor>
                        {
                            0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ?
                                <h4 style={{ position: 'fixed', marginLeft: 10, top: 415 }}>转场节点导航</h4> : null
                        }
                        {
                            0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ?
                                <Anchor
                                    targetOffset={(state.editor.screen.height - 400) / 2}
                                    getContainer={() => document.getElementById('correct-list')}
                                    style={{
                                        position: 'fixed',
                                        marginLeft: 10,
                                        top: 440,
                                        height: 200,
                                        overflow: 'scroll'
                                    }}
                                >
                                    {
                                        getLinkJsx()
                                    }
                                </Anchor> : null
                        }
                    </div>
                    : null
            }
        </Content>
    );
};
