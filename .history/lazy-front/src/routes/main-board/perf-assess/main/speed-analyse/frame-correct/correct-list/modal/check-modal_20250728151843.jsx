/**
* @file check-modal.jsx 检查模式弹窗
* @create 王家麒@2022.04.29
*/
import { Image, Button, Space, Tooltip, message } from 'antd';
import { SelectOutlined, EyeOutlined } from '@ant-design/icons';

const frameDetail = (record, rowIndex, type, { state, dispatch }) => {
    let frameJsx = [];
    // 标题
    frameJsx.push(
        <li style={{ writingMode: 'vertical-lr', flexDirection: 'column', textAlign: 'center' }}>
            {'first' === type ? '首帧校准' : '尾帧校准'}
        </li>
    );
    // 生成每一帧图片
    for (let frameItem of 'first' === type ? record.firstFrameSummary : record.lastFrameSummary) {
        let { index, name, timestamp, frame } = frameItem;
        let maskMsg = '';
        switch (type) {
            case 'first':
                if (index === record.smartFirstFrame) {
                    maskMsg = '智能首帧';
                }
                if (index === record.manualFirstFrame) {
                    maskMsg = '人工首帧';
                }
                break;
            case 'last':
                if (index === record.smartLastFrame) {
                    maskMsg = '智能尾帧';
                }
                if (index === record.manualLastFrame) {
                    maskMsg = '人工尾帧';
                }
                break;
            default: break;
        }
        frameJsx.push(
            <li
                style={{ margin: '20px 5px' }}
                key={index}
            >
                <div>
                    <Tooltip
                        title={
                            <div>
                                <div>index: {index}</div>
                                <div>{name}</div>
                            </div>
                        }
                    >
                        <Image
                            placeholder
                            style={{
                                width: (state.editor.screen.width - 246) / 10,
                                border: '1px solid'
                            }}
                            src={frame}
                            preview={{
                                mask: (
                                    <Space direction='vertical'>
                                        <Button
                                            shape='round'
                                            icon={<SelectOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                switch (type) {
                                                    case 'first':
                                                        record.manualFirstFrame = index;
                                                        record.manualFirstTimestamp = timestamp;
                                                        record.manualFirstStatus = 1;
                                                        break;
                                                    case 'last':
                                                        record.manualLastFrame = index;
                                                        record.manualLastTimestamp = timestamp;
                                                        record.manualLastStatus = 1;
                                                        break;
                                                    default: break;
                                                }
                                                let correctList =
                                                    state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                correctList[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        correctList
                                                    }
                                                });
                                                dispatch({
                                                    type: 'editor/speedFrameCorrect',
                                                    payload: {
                                                        recordId: record.recordId,
                                                        type,
                                                        frame: index,
                                                        timestamp: timestamp
                                                    }
                                                });
                                                message.success('已校准');
                                            }}
                                        >
                                            校准
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<EyeOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        previewIndex: index
                                                    }
                                                });
                                            }}
                                        >
                                            预览
                                        </Button>
                                    </Space>
                                ),
                                visible: state.editor.perfAssess.operator.speedAnalyse.previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                previewIndex: -1
                                            }
                                        });
                                    }
                                }
                            }}
                        />
                    </Tooltip>
                </div>
                {
                    '' !== maskMsg ? (
                        <div
                            style={{
                                textAlign: 'center',
                                zIndex: 998,
                                marginTop: -22,
                                background: 'rgba(0, 0, 0, 0.5)',
                                color: 'white',
                                position: 'relative'
                            }}
                        >
                            {maskMsg}
                        </div>
                    ) : null
                }
            </li>
        );
    }
    return (
        <ul
            style={{
                display: 'inline-flex', flexWrap: 'wrap',
                justifyContent: 'flex-start', listStyle: 'none'
            }}
        >
            {frameJsx}
        </ul>
    );
};

export default ({ record, index, dispatch, state }) => {
    return (
        <div>
            {frameDetail(record, index, 'first', { state, dispatch })}
            <br />
            {frameDetail(record, index, 'last', { state, dispatch })}
        </div>
    );
};
