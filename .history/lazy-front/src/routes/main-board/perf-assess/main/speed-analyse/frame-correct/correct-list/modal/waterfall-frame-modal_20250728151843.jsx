/**
 * @file waterfall-frame-modal.jsx 瀑布帧校准
 * @create 王家麒@2022.06.20
 */
import { Tag, Image, Row, Button, Space, Tooltip, message } from 'antd';
import { PicLeftOutlined, EyeOutlined, PicRightOutlined } from '@ant-design/icons';
import LazyLoad from 'react-lazyload';

import { effectHandler } from './util/effect-handler';
import CorrectModal from './operator/index';
import WaterfallFrameModal from './waterfall-frame-modal';

const lazyLoadImg = '../../../../../../../../image/loading.png';

// 生成帧图片
const frameDetail = (record, rowIndex, effectList, { state, dispatch, modal }) => {
    let frameJsx = [];
    // 生成每一帧图片
    for (let index = 0; index < record.frameList.length; index++) {
        let border = '1px solid';
        let maskMsg = '';
        if (record.frameList[index].name) {
            border = '5px solid #2F4ED6';
        }
        if (index === record.smartFirstFrame) {
            maskMsg = '智能首帧';
        }
        if (index === record.smartLastFrame) {
            maskMsg = '智能尾帧';
        }
        if (index === record.manualFirstFrame) {
            maskMsg = '人工首帧';
            border = '5px solid red';
        }
        if (index === record.manualLastFrame) {
            maskMsg = '人工尾帧';
            border = '5px solid red';
        }
        frameJsx.push(
            <li
                id={`frameIndex${index}`}
                style={{ margin: '20px 5px' }}
                key={index}
            >
                <div
                    style={{
                        height: state.editor.screen.height / 3.5,
                        width: (state.editor.screen.width - 270) / 12,
                    }}
                >
                    <LazyLoad
                        resize
                        scrollContainer={document.getElementsByClassName('ulImgs')}
                        overflow
                        placeholder={
                            <img
                                style={{
                                    height: state.editor.screen.height / 3.5,
                                    width: (state.editor.screen.width - 270) / 12,
                                }}
                                src={lazyLoadImg}
                                alt='logo'
                            />
                        }
                    >
                        <Tooltip
                            title={
                                <div>
                                    <div>index: {index}</div>
                                    <div>{record.frameList[index].name}</div>
                                </div>
                            }
                        >
                            <Image
                                placeholder
                                style={{
                                    height: state.editor.screen.height / 3.5,
                                    maxWidth: (state.editor.screen.width - 270) / 12,
                                    border
                                }}
                                src={record.frameList[index].frame}
                                preview={{
                                    mask: (
                                        <Space direction='vertical'>
                                            <Button
                                                shape='round'
                                                icon={<PicLeftOutlined />}
                                                style={{ opacity: 0.8 }}
                                                onClick={() => {
                                                    record.manualFirstFrame = index;
                                                    record.manualFirstTimestamp = record.frameList[index].timestamp;
                                                    record.manualFirstStatus = 1;
                                                    let correctList =
                                                        state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                    correctList[rowIndex] = record;
                                                    dispatch({
                                                        type: 'editor/setSpeedAnalyse',
                                                        payload: {
                                                            correctList
                                                        }
                                                    });
                                                    dispatch({
                                                        type: 'editor/speedFrameCorrect',
                                                        payload: {
                                                            recordId: record.recordId,
                                                            type: 'first',
                                                            frame: index,
                                                            timestamp: record.frameList[index].timestamp
                                                        }
                                                    });
                                                    message.success('首帧已设定');
                                                    modal.update({
                                                        content: (
                                                            <WaterfallFrameModal
                                                                record={record}
                                                                rowIndex={rowIndex}
                                                                dispatch={dispatch}
                                                                state={state}
                                                                modal={modal}
                                                            />
                                                        )
                                                    });
                                                }}
                                            >
                                                首帧
                                            </Button>
                                            <Button
                                                shape='round'
                                                icon={<EyeOutlined />}
                                                style={{ opacity: 0.8 }}
                                                onClick={() => {
                                                    dispatch({
                                                        type: 'editor/setSpeedAnalyse',
                                                        payload: {
                                                            previewIndex: index
                                                        }
                                                    });
                                                    modal.update({
                                                        content: (
                                                            <WaterfallFrameModal
                                                                record={record}
                                                                rowIndex={rowIndex}
                                                                dispatch={dispatch}
                                                                state={state}
                                                                modal={modal}
                                                            />
                                                        )
                                                    });
                                                }}
                                            >
                                                预览
                                            </Button>
                                            <Button
                                                shape='round'
                                                icon={<PicRightOutlined />}
                                                style={{ opacity: 0.8 }}
                                                onClick={() => {
                                                    record.manualLastFrame = index;
                                                    record.manualLastTimestamp = record.frameList[index].timestamp;
                                                    record.manualLastStatus = 1;
                                                    let correctList =
                                                        state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                    correctList[rowIndex] = record;
                                                    dispatch({
                                                        type: 'editor/setSpeedAnalyse',
                                                        payload: {
                                                            correctList
                                                        }
                                                    });
                                                    dispatch({
                                                        type: 'editor/speedFrameCorrect',
                                                        payload: {
                                                            recordId: record.recordId,
                                                            type: 'last',
                                                            frame: index,
                                                            timestamp: record.frameList[index].timestamp
                                                        }
                                                    });
                                                    message.success('尾帧已设定');
                                                    modal.update({
                                                        content: (
                                                            <WaterfallFrameModal
                                                                record={record}
                                                                rowIndex={rowIndex}
                                                                dispatch={dispatch}
                                                                state={state}
                                                                modal={modal}
                                                            />
                                                        )
                                                    });
                                                }}
                                            >
                                                尾帧
                                            </Button>
                                        </Space>
                                    ),
                                    visible: state.editor.perfAssess.operator.speedAnalyse.previewIndex === index,
                                    onVisibleChange: (visible) => {
                                        if (!visible) {
                                            dispatch({
                                                type: 'editor/setSpeedAnalyse',
                                                payload: {
                                                    previewIndex: -1
                                                }
                                            });
                                            modal.update({
                                                content: (
                                                    <WaterfallFrameModal
                                                        record={record}
                                                        rowIndex={rowIndex}
                                                        dispatch={dispatch}
                                                        state={state}
                                                        modal={modal}
                                                    />
                                                )
                                            });
                                        }
                                    }
                                }}
                            />
                        </Tooltip>
                    </LazyLoad>
                </div>
                <div
                    style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                >
                    <Tag
                        color="orange"
                        style={{ padding: '0 8px' }}
                    >
                        {record.frameList[index].timestamp - record.frameList[0].timestamp} ms
                    </Tag>
                </div>
                {
                    '' !== maskMsg ? (
                        <div
                            style={{
                                textAlign: 'center',
                                zIndex: 998,
                                marginTop: -50,
                                background: 'rgba(0, 0, 0, 0.5)',
                                color: 'white',
                                position: 'relative'
                            }}
                        >
                            {maskMsg}
                        </div>
                    ) : null
                }
            </li>
        );
    }
    return (
        <ul
            className='ulImgs'
            style={{
                display: 'inline-flex', flexWrap: 'wrap',
                justifyContent: 'flex-start', listStyle: 'none'
            }}
        >
            {frameJsx}
        </ul>
    );
};

export default ({ record, rowIndex, dispatch, state, modal }) => {
    let effectList = effectHandler(record);
    return (
        <div style={{ height: state.editor.screen.height - 200, overflowY: 'scroll' }}>
            <CorrectModal
                record={record}
                rowIndex={rowIndex}
                effectList={effectList}
                currentModal='waterfall'
                modal={modal}
                state={state}
                dispatch={dispatch}
            />
            <Row style={{ marginTop: 25 }}>
                {frameDetail(record, rowIndex, effectList, { state, dispatch, modal })}
            </Row>
        </div>
    );
};
