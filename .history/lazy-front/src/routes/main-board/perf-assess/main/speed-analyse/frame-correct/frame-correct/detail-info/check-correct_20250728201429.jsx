/**
* @file check-coorect.jsx 检查模式弹窗
* @create 王家麒@2022.04.29
*/
import React from 'react';
import { Image, Tag, Button, Layout, Tooltip, message } from 'antd';
import { EyeOutlined, CaretLeftFilled, CaretRightFilled, RotateRightOutlined, RotateLeftOutlined } from '@ant-design/icons';

import styles from '../../../../../../../../assets/css/perf-assess/speed-analyse.module.css';
import BarSlider from '../component/bar-slider';

const { Content } = Layout;

const frameDetail = (record, rowIndex, type, { state, dispatch }) => {
    let page = 'first' === type ? state.editor.perfAssess.operator.speedAnalyse.firstPage :
        state.editor.perfAssess.operator.speedAnalyse.lastPage;

    let totalCount = record.frameList.length;
    let pageNum = state.editor.perfAssess.isPerfPlanCollapsed ? 7 : 5;
    let frameJsx = [];

    // 生成每一帧图片
    for (let index = page; index < page + pageNum; index++) {
        if (index >= totalCount) {
            break;
        }
        let border = '1px solid';
        let maskMsg = '';
        let id = '';
        if (-1 !== state.editor.perfAssess.operator.speedAnalyse.stageList.indexOf(index)) {
            border = '3px dashed orange';
            maskMsg = '转场点';
        }
        if ('first' === type) {
            if (index === record.smartFirstFrame) {
                border = '3px dashed #ff0000';
                maskMsg = '智能首帧';
                id = `first-frame-${rowIndex}`;
            }
            if (index === record.manualFirstFrame) {
                border = '3px solid #ff0000';
                maskMsg = '人工首帧';
                id = `first-frame-${rowIndex}`;
            }
        }
        else {
            if (index === record.smartLastFrame) {
                border = '3px dashed #ff0000';
                maskMsg = '智能尾帧';
                id = `last-frame-${rowIndex}`;
            }
            if (index === record.manualLastFrame) {
                border = '3px solid #ff0000';
                maskMsg = '人工尾帧';
                id = `last-frame-${rowIndex}`;
            }
        }

        frameJsx.push(
            record.frameList[index].frame ?
                <Tooltip
                    placement='top'
                    title={
                        <div>
                            <div>index: {index}</div>
                            <div>{maskMsg}</div>
                        </div>
                    }
                >
                    <li
                        id={id}
                        key={index}
                        className={styles.frame_li}
                        style={{
                            width: state.editor.perfAssess.isPerfPlanCollapsed ? '13.428%' : '19.2%'
                        }}
                    >
                        <div
                            style={{
                                position: 'absolute',
                                left: 0,
                                zIndex: 998,
                                width: border.slice(0, 4),
                                height: (state.editor.screen.height - 300) / 2,
                                border
                            }}>
                        </div>
                        <div
                            style={{
                                position: 'absolute',
                                right: 0,
                                zIndex: 998,
                                width: border.slice(0, 4),
                                height: (state.editor.screen.height - 300) / 2,
                                border
                            }}>
                        </div>
                        <div
                            style={{
                                position: 'absolute',
                                top: 0,
                                zIndex: 998,
                                width: '100%',
                                height: border.slice(0, 4),
                                border
                            }}>
                        </div>
                        <div
                            style={{
                                position: 'absolute',
                                bottom: 27,
                                zIndex: 998,
                                width: '100%',
                                height: border.slice(0, 4),
                                border
                            }}>
                        </div>
                        <Image
                            placeholder
                            width={'100%'}
                            height={(state.editor.screen.height - 300) / 2}
                            src={record.frameList[index].frame}
                            preview={{
                                mask: (
                                    <Button
                                        shape='round'
                                        icon={<EyeOutlined />}
                                        style={{
                                            position: 'absolute',
                                            bottom: 35
                                        }}
                                        onClick={() => {
                                            dispatch({
                                                type: 'editor/setSpeedAnalyse',
                                                payload: {
                                                    previewIndex: index
                                                }
                                            });
                                        }}
                                    >
                                        预览我
                                    </Button>
                                ),
                                visible: state.editor.perfAssess.operator.speedAnalyse.previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                previewIndex: -1
                                            }
                                        });
                                    }
                                }
                            }}
                            onClick={() => {
                                dispatch({
                                    type: 'editor/setSpeedAnalyse',
                                    payload: {
                                        isFrame: 'first' === type ? 1 : 2
                                    }
                                });
                            }}
                            onDoubleClick={() => {
                                if (0 === record.isValid) {
                                    switch (type) {
                                        case 'first':
                                            record.manualFirstFrame = index;
                                            record.manualFirstTimestamp = record.frameList[index].timestamp;
                                            record.manualFirstStatus = 1;
                                            break;
                                        case 'last':
                                            record.manualLastFrame = index;
                                            record.manualLastTimestamp = record.frameList[index].timestamp;
                                            record.manualLastStatus = 1;
                                            break;
                                        default: break;
                                    }
                                    let correctList =
                                        state.editor.perfAssess.operator.speedAnalyse.correctList;
                                    correctList[rowIndex] = record;
                                    dispatch({
                                        type: 'editor/setSpeedAnalyse',
                                        payload: {
                                            correctList
                                        }
                                    });
                                    dispatch({
                                        type: 'editor/speedFrameCorrect',
                                        payload: {
                                            recordId: record.recordId,
                                            type,
                                            frame: index,
                                            timestamp: record.frameList[index].timestamp
                                        }
                                    });
                                } else {
                                    message.info('该组分帧图已废弃');
                                }
                            }}
                        />

                        {
                            '' !== maskMsg ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                        width: '100%',
                                        zIndex: 998,
                                        marginTop: -22,
                                        background: 'rgba(0, 0, 0, 0.5)',
                                        color: 'white',
                                        position: 'relative'
                                    }}
                                >
                                    {maskMsg}
                                </div>
                            ) : null
                        }
                        <div
                            style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                        >
                            <Tag
                                color="orange"
                                style={{ padding: '0 8px' }}
                            >
                                {record.frameList[index].timestamp - record.frameList[0].timestamp} ms
                            </Tag>
                        </div>
                    </li>
                </Tooltip>
                : null
        );
    }

    return frameJsx;
};

const left = (state, dispatch, type) => {
    let page = 'first' === type ? state.editor.perfAssess.operator.speedAnalyse.firstPage :
        state.editor.perfAssess.operator.speedAnalyse.lastPage;
    let pageNum = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
    return (
        <div
            style={{
                position: 'relative',
                width: '3%',
                float: 'left',
                height: (state.editor.screen.height - 300) / 2
            }}
        >
            {/* 跳转上一个趋势点 */}
            {
                undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList && 0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ?
                    <Tooltip title={'跳转上一个转场趋势点'}>
                        <RotateLeftOutlined
                            style={{
                                position: 'absolute',
                                top: '40%',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                let payload = {};
                                let changePage = state.editor.perfAssess.operator.speedAnalyse.isPerfPlanCollapsed ? 3 : 2;
                                if ('first' === type) {
                                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                                    let node = 0;
                                    for (let [index, item] of stageList.entries()) {
                                        if (item > state.editor.perfAssess.operator.speedAnalyse.firstPage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (state.editor.perfAssess.operator.speedAnalyse.firstPage > stageList[stageList.length - 1]) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                firstPage: stageList[stageList.length - 1] > changePage ?
                                                    stageList[stageList.length - 1] - changePage : 0,
                                                firstNode: stageList.length - 1
                                            }
                                        });
                                    } else if (node > 0) {
                                        payload.firstPage = stageList[node - 1] > changePage ? stageList[node - 1] - changePage : stageList[node - 1];
                                        payload.firstNode = node - 1;
                                        payload.isFrame = 1;
                                    } else {
                                        message.info('已跳转至第一个转场趋势点')
                                    }
                                } else {
                                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                                    let node = 0;
                                    for (let [index, item] of stageList.entries()) {
                                        if (item > state.editor.perfAssess.operator.speedAnalyse.lastPage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (state.editor.perfAssess.operator.speedAnalyse.lastPage > stageList[stageList.length - 1]) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                lastPage: stageList[stageList.length - 1] > changePage ?
                                                    stageList[stageList.length - 1] - changePage : 0,
                                                lastNode: stageList.length - 1
                                            }
                                        });
                                    } else if (node > 0) {
                                        payload.lastPage = stageList[node - 1] > changePage ? stageList[node - 1] - changePage : stageList[node - 1];
                                        payload.lastNode = node - 1;
                                        payload.isFrame = 2;
                                    } else {
                                        message.info('已跳转至第一个转场趋势点')
                                    }
                                }
                                dispatch({ type: 'editor/setSpeedAnalyse', payload });
                            }}
                        />
                    </Tooltip> : null
            }
            {/* 跳转上页 */}
            <CaretLeftFilled
                style={{
                    position: 'absolute',
                    bottom: undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList && 0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ? '40%' : '50%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                    cursor: 'pointer'
                }}
                onClick={() => {
                    let payload = {};
                    if ('first' === type) {
                        payload.firstPage = page - pageNum < 0 ? 0 : page - pageNum;
                    }
                    else {
                        payload.lastPage = page - pageNum < 0 ? 0 : page - pageNum;
                    }
                    dispatch({ type: 'editor/setSpeedAnalyse', payload });
                }}
            />
        </div>
    );
}

const right = (state, dispatch, record, type) => {
    let page = 'first' === type ? state.editor.perfAssess.operator.speedAnalyse.firstPage :
        state.editor.perfAssess.operator.speedAnalyse.lastPage;
    let totalCount = record.frameList.length;
    let pageNum = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
    return (
        <div
            style={{
                position: 'relative',
                width: '3%',
                float: 'left',
                height: (state.editor.screen.height - 300) / 2
            }}
        >
            {/* 跳转下一个转场趋势点 */}
            {
                undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList && 0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ?
                    <Tooltip title={'跳转下一个转场趋势点'}>
                        <RotateRightOutlined
                            style={{
                                position: 'absolute',
                                top: '40%',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                let changePage = state.editor.perfAssess.operator.speedAnalyse.isPerfPlanCollapsed ? 3 : 2;
                                let payload = {};
                                if ('first' === type) {
                                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                                    let node = stageList.length - 1;
                                    for (let [index, item] of stageList.entries()) {
                                        if (index !== stageList.length - 1 && item > state.editor.perfAssess.operator.speedAnalyse.firstPage + changePage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (node <= stageList.length - 1) {
                                        payload.firstPage = stageList[node] > changePage ? stageList[node] - changePage : stageList[node];
                                        payload.firstNode = node;
                                        payload.isFrame = 1;
                                        if (node === stageList.length - 1) {
                                            message.info('已跳转至最后一个转场趋势点');
                                        }
                                    }
                                }
                                else {
                                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                                    let node = stageList.length - 1;
                                    for (let [index, item] of stageList.entries()) {
                                        if (index !== stageList.length - 1 && item > state.editor.perfAssess.operator.speedAnalyse.lastPage + changePage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (node <= stageList.length - 1) {
                                        payload.lastPage = stageList[node] > changePage ? stageList[node] - changePage : stageList[node];
                                        payload.lastNode = node;
                                        payload.isFrame = 2;
                                        if (node === stageList.length - 1) {
                                            message.info('已跳转至最后一个转场趋势点');
                                        }
                                    }
                                }
                                dispatch({ type: 'editor/setSpeedAnalyse', payload });
                            }}
                        />
                    </Tooltip> : null
            }
            {/* 跳转后页 */}
            <CaretRightFilled
                style={{
                    position: 'absolute',
                    bottom: undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList && 0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length ? '40%' : '50%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                    cursor: 'pointer'
                }}
                onClick={() => {
                    let payload = {};
                    if ('first' === type) {
                        payload.firstPage = page + pageNum >= totalCount ? totalCount - pageNum : page + pageNum;
                    }
                    else {
                        payload.lastPage = page + pageNum >= totalCount ? totalCount - pageNum : page + pageNum;
                    }
                    dispatch({ type: 'editor/setSpeedAnalyse', payload });
                }}
            />
        </div>
    );
}

export default ({ dispatch, state, rowIndex, record }) => {
    // 第一次打开校准，智能首尾帧变为人工首尾帧
    let isCorrect = false;
    if (0 === record.manualFirstStatus) {
        record.manualFirstFrame = record.smartFirstFrame;
        record.manualFirstTimestamp = record.smartFirstTimestamp;
        record.manualFirstStatus = 1;
        dispatch({
            type: 'editor/speedFrameCorrect',
            payload: {
                recordId: record.recordId,
                type: 'first',
                frame: record.smartFirstFrame,
                timestamp: record.smartFirstTimestamp
            }
        });
        isCorrect = true;
    }
    if (0 === record.manualLastStatus) {
        record.manualLastFrame = record.smartLastFrame;
        record.manualLastTimestamp = record.smartLastTimestamp;
        record.manualLastStatus = 1;
        dispatch({
            type: 'editor/speedFrameCorrect',
            payload: {
                recordId: record.recordId,
                type: 'last',
                frame: record.smartLastFrame,
                timestamp: record.smartLastTimestamp
            }
        });
        isCorrect = true;
    }
    // 更新数据
    if (isCorrect) {
        let correctList = state.editor.perfAssess.operator.speedAnalyse.correctList;
        correctList[rowIndex] = record;
        dispatch({
            type: 'editor/setSpeedAnalyse',
            payload: {
                correctList
            }
        });
        let firstIndex = 0 === record.manualFirstStatus ? record.smartFirstFrame : record.manualFirstFrame;
        let lastIndex = 0 === record.manualFirstStatus ? record.smartLastFrame : record.manualLastFrame;
        let changePage = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
        dispatch({
            type: 'editor/setSpeedAnalyse',
            payload: {
                correctTaskIndex: rowIndex,
                isFrame: 1,
                firstPage: firstIndex - changePage < 0 ? 0 : firstIndex - changePage,
                lastPage: lastIndex - changePage < 0 ? 0 : lastIndex - changePage
            }
        });
    }

    const getMainFrame = (record) => {
        let changePage = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
        // 确定主显示帧
        let firstIndex = 0 === record.manualFirstStatus ? record.smartFirstFrame : record.manualFirstFrame;
        let lastIndex = 0 === record.manualFirstStatus ? record.smartLastFrame : record.manualLastFrame;
        dispatch({
            type: 'editor/setSpeedAnalyse',
            payload: {
                firstPage: firstIndex - changePage < 0 ? 0 : firstIndex - changePage,
                lastPage: lastIndex - changePage < 0 ? 0 : lastIndex - changePage
            }
        });
    };
    /**
    * 捕获键盘按下，满足校准页面切换
    */
    const onKeyDown = (e) => {
        let {
            correctTaskIndex,
            correctTablePage,
            correctTotalCount,
            firstPage,
            lastPage,
            correctList
        } = state.editor.perfAssess.operator.speedAnalyse;
        let pageSize = 10;
        let changePage = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
        correctTaskIndex = parseInt(correctTaskIndex, 10);
        let pageNum = state.editor.perfAssess.isPerfPlanCollapsed ? 3 : 2;
        if (-1 === Object.keys(correctList[correctTaskIndex]).indexOf('frameList')) {
            return false;
        }
        if (-1 !== Object.keys(correctList[correctTaskIndex]).indexOf('frameList')
            && 0 === correctList[correctTaskIndex].frameList.length) {
            return false;
        }
        let totalCount = correctList[correctTaskIndex].frameList.length;
        // w
        // 向上切换文件
        if (e.keyCode === 87) {
            if (state.editor.perfAssess.operator.speedAnalyse.isFrame === 0) {
                dispatch({
                    type: 'editor/setSpeedAnalyse',
                    payload: {
                        isFrame: 1
                    }
                });
            } else if (state.editor.perfAssess.operator.speedAnalyse.isFrame === 2) {
                dispatch({
                    type: 'editor/setSpeedAnalyse',
                    payload: {
                        isFrame: 1
                    }
                });
            } else {
                if (correctTaskIndex === 0 && correctTablePage === 0) {
                    message.info('已到第一条');
                } else if (correctTaskIndex !== 0) {
                    dispatch({
                        type: 'editor/setSpeedAnalyse',
                        payload: {
                            isFrame: 2,
                            correctTaskIndex: correctTaskIndex - 1,
                            stageList: correctList[correctTaskIndex - 1].stageList
                        }
                    });
                    dispatch({
                        type: 'editor/getSpeedFrameList',
                        payload: {
                            state,
                            dispatch,
                            record: correctList[correctTaskIndex - 1],
                            rowIndex: correctTaskIndex - 1,
                        }
                    });
                    getMainFrame(correctList[correctTaskIndex - 1]);
                } else if (correctTablePage !== 0) {
                    dispatch({
                        type: 'editor/setSpeedAnalyse',
                        payload: {
                            isFrame: 2
                        }
                    });
                    dispatch({
                        type: 'editor/getSpeedCorrectList',
                        payload: {
                            state,
                            dispatch,
                            index: pageSize - 1,
                            page: correctTablePage - 1
                        }
                    });
                }
            }

        }
        // s
        // 向下切换文件
        if (e.keyCode === 83) {
            if (state.editor.perfAssess.operator.speedAnalyse.isFrame === 0) {
                dispatch({
                    type: 'editor/setSpeedAnalyse',
                    payload: {
                        isFrame: 2
                    }
                });
            } else if (state.editor.perfAssess.operator.speedAnalyse.isFrame === 1) {
                dispatch({
                    type: 'editor/setSpeedAnalyse',
                    payload: {
                        isFrame: 2
                    }
                });
            } else {
                if ((correctTablePage * pageSize + (correctTaskIndex + 1)) === correctTotalCount) {
                    message.info('已到最后一条');
                } else if (correctTaskIndex !== pageSize - 1) {
                    dispatch({
                        type: 'editor/setSpeedAnalyse',
                        payload: {
                            correctTaskIndex: correctTaskIndex + 1,
                            isFrame: 1,
                            stageList: correctList[correctTaskIndex + 1].stageList
                        }
                    });
                    dispatch({
                        type: 'editor/getSpeedFrameList',
                        payload: {
                            state,
                            dispatch,
                            record: correctList[correctTaskIndex + 1],
                            rowIndex: correctTaskIndex + 1,
                        }
                    });
                    getMainFrame(state.editor.perfAssess.operator.speedAnalyse.correctList[correctTaskIndex + 1]);
                } else if (correctTaskIndex === pageSize - 1 &&
                    (correctTablePage * pageSize + (correctTaskIndex + 1)) !== correctTotalCount) {
                    dispatch({
                        type: 'editor/setSpeedAnalyse',
                        payload: {
                            isFrame: 1
                        }
                    });
                    dispatch({
                        type: 'editor/getSpeedCorrectList',
                        payload: {
                            state,
                            dispatch,
                            index: 0,
                            page: correctTablePage + 1
                        }
                    });
                }
            }
        }
        // a
        // 帧向左翻页
        if (e.keyCode === 65) {
            dispatch({
                type: 'editor/setSpeedAnalyse',
                payload: {
                    firstPage: 2 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? firstPage : firstPage - pageNum < 0 ? 0 : firstPage - pageNum,
                    lastPage: 1 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? lastPage : lastPage - pageNum < 0 ? 0 : lastPage - pageNum
                }
            });
        }
        // d
        // 帧向右翻页
        if (e.keyCode === 68) {
            dispatch({
                type: 'editor/setSpeedAnalyse',
                payload: {
                    firstPage: 2 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? firstPage : firstPage + pageNum >= totalCount ? totalCount - pageNum : firstPage + pageNum,
                    lastPage: 1 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? lastPage : lastPage + pageNum >= totalCount ? totalCount - pageNum : lastPage + pageNum
                }
            });
        }
        // esc
        // 回到人工/智能首尾帧
        if (e.keyCode === 27) {
            getMainFrame(correctList[correctTaskIndex]);
        }

        // Q
        // 跳转到上一个重要节点
        if (e.keyCode === 81) {
            if (0 !== Object.keys(record).length &&
                undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
                null !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
                0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length) {
                let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                if (1 === state.editor.perfAssess.operator.speedAnalyse.isFrame) {
                    let node = 0;
                    for (let [index, item] of stageList.entries()) {
                        if (item > state.editor.perfAssess.operator.speedAnalyse.firstPage) {
                            node = index;
                            break;
                        }
                    }
                    // 最左情况
                    // 最右情况
                    // 中部情况
                    if (state.editor.perfAssess.operator.speedAnalyse.firstPage < stageList[0]) {
                        message.info('已无转场趋势点');
                    } else if (state.editor.perfAssess.operator.speedAnalyse.firstPage > stageList[stageList.length - 1]) {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                firstPage: stageList[stageList.length - 1] > changePage ?
                                    stageList[stageList.length - 1] - changePage : 0,
                                firstNode: stageList.length - 1
                            }
                        });
                    } else {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                firstPage: state.editor.perfAssess.operator.speedAnalyse.stageList[node - 1] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[node - 1] - changePage : 0,
                                firstNode: node - 1
                            }
                        });
                    }
                } else if (2 === state.editor.perfAssess.operator.speedAnalyse.isFrame) {
                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                    let node = 0;
                    for (let [index, item] of state.editor.perfAssess.operator.speedAnalyse.stageList.entries()) {
                        if (item > state.editor.perfAssess.operator.speedAnalyse.lastPage) {
                            node = index;
                            break;
                        }
                    }
                    if (state.editor.perfAssess.operator.speedAnalyse.lastPage < stageList[0]) {
                        message.info('已无转场趋势点');
                    } else if (state.editor.perfAssess.operator.speedAnalyse.lastPage > stageList[stageList.length - 1]) {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                lastPage: state.editor.perfAssess.operator.speedAnalyse.stageList[stageList.length - 1] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[stageList.length - 1] - changePage : 0,
                                lastNode: stageList.length - 1
                            }
                        });
                    } else {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                lastPage: state.editor.perfAssess.operator.speedAnalyse.stageList[node - 1] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[node - 1] - changePage : 0,
                                lastNode: node - 1
                            }
                        });
                    }
                }
            }
        }
        // E
        // 校准跳转到下一个重要节点
        if (e.keyCode === 69) {
            if (0 !== Object.keys(record).length &&
                undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
                null !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
                0 !== state.editor.perfAssess.operator.speedAnalyse.stageList.length) {
                let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                if (1 === state.editor.perfAssess.operator.speedAnalyse.isFrame) {
                    let node = stageList.length - 1;
                    for (let [index, item] of stageList.entries()) {
                        if (index !== stageList.length - 1 && item > state.editor.perfAssess.operator.speedAnalyse.firstPage + changePage) {
                            node = index;
                            break;
                        }
                    }
                    // 最右情况
                    // 最左情况
                    // 中部情况
                    if (state.editor.perfAssess.operator.speedAnalyse.firstPage + changePage >= stageList[stageList.length - 1]) {
                        message.info('已无转场趋势点');
                    } else if (state.editor.perfAssess.operator.speedAnalyse.firstPage + changePage < stageList[0]) {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                firstPage: state.editor.perfAssess.operator.speedAnalyse.stageList[0] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[0] - changePage : 0,
                                firstNode: 0
                            }
                        });
                    } else {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                firstPage: state.editor.perfAssess.operator.speedAnalyse.stageList[node] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[node] - changePage : 0,
                                firstNode: node
                            }
                        });
                    }
                } else if (2 === state.editor.perfAssess.operator.speedAnalyse.isFrame) {
                    let stageList = state.editor.perfAssess.operator.speedAnalyse.stageList;
                    let node = stageList.length - 1;
                    for (let [index, item] of stageList.entries()) {
                        if (index !== stageList.length - 1 && item > state.editor.perfAssess.operator.speedAnalyse.lastPage + changePage) {
                            node = index;
                            break;
                        }
                    }
                    if (state.editor.perfAssess.operator.speedAnalyse.lastPage + changePage >= stageList[stageList.length - 1]) {
                        message.info('已无转场趋势点');
                    } else if (state.editor.perfAssess.operator.speedAnalyse.lastPage + changePage < stageList[0]) {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                lastPage: state.editor.perfAssess.operator.speedAnalyse.stageList[0] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[0] - changePage : 0,
                                lastNode: 0
                            }
                        });
                    } else {
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                lastPage: state.editor.perfAssess.operator.speedAnalyse.stageList[node] > changePage ?
                                    state.editor.perfAssess.operator.speedAnalyse.stageList[node] - changePage : 0,
                                lastNode: node
                            }
                        });
                    }
                }
            }
        }
    };
    React.useEffect(() => {
        window.addEventListener('keydown', onKeyDown); // 添加全局事件
        return () => {
            window.removeEventListener('keydown', onKeyDown); // 销毁
        };
    }, []);
    return (
        <Content
            id={'check-correct-list'}
            style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}
        >
            {
                -1 !== Object.keys(record).indexOf('frameList') && 0 !== Object.keys(record).length ?
                    <div
                        style={{
                            position: 'relative',
                            width: state.editor.screen.width <= 1440 || state.editor.perfAssess.isPerfPlanCollapsed ? '100%' : '80%',
                            marginLeft: state.editor.screen.width <= 1440 || state.editor.perfAssess.isPerfPlanCollapsed ? '0%' : '10%',
                            marginTop: 0,
                            height: (state.editor.screen.height - 250) / 2
                        }}
                    >
                        {left(state, dispatch, 'first')}
                        <div style={{ width: '94%', float: 'left' }}>
                            <ul
                                style={{
                                    width: '100%',
                                    padding: 0,
                                    margin: 0,
                                    opacity: 2 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? 0.2 : 1,
                                    display: 'inline-flex', flexWrap: 'wrap',
                                    justifyContent: 'flex-start', listStyle: 'none'
                                }}
                            >
                                {frameDetail(record, rowIndex, 'first', { state, dispatch })}
                            </ul>
                        </div>

                        {right(state, dispatch, record, 'first')}
                    </div>
                    : null
            }
            {
                -1 !== Object.keys(record).indexOf('frameList') && 0 !== Object.keys(record).length ?
                    <BarSlider state={state} dispatch={dispatch} record={record} type={'first'} /> : null
            }
            {
                -1 !== Object.keys(record).indexOf('frameList') && 0 !== Object.keys(record).length ?
                    <div
                        style={{
                            position: 'relative',
                            width: state.editor.screen.width <= 1440 || state.editor.perfAssess.isPerfPlanCollapsed ? '100%' : '80%',
                            marginLeft: state.editor.screen.width <= 1440 || state.editor.perfAssess.isPerfPlanCollapsed ? '0%' : '10%',
                            marginTop: 3,
                            height: (state.editor.screen.height - 250) / 2
                        }}
                    >
                        {left(state, dispatch, 'last')}
                        <div style={{ width: '94%', float: 'left' }}>
                            <ul
                                style={{
                                    width: '100%',
                                    padding: 0,
                                    margin: 0,
                                    opacity: 1 === state.editor.perfAssess.operator.speedAnalyse.isFrame ? 0.2 : 1,
                                    display: 'inline-flex', flexWrap: 'wrap',
                                    justifyContent: 'flex-start', listStyle: 'none'
                                }}
                            >
                                {frameDetail(record, rowIndex, 'last', { state, dispatch })}
                            </ul>
                        </div>
                        {right(state, dispatch, record, 'last')}
                    </div> : null
            }
            {
                -1 !== Object.keys(record).indexOf('frameList') && 0 !== Object.keys(record).length ?
                    <BarSlider state={state} dispatch={dispatch} record={record} type={'last'} /> : null
            }
        </Content>
    );
};
