/**
 * @file frame-correct-modal.jsx 帧校准弹窗
 * @create 王家麒@2022.06.14
 */
import { Tag, Image, Button, Space, Tooltip, Row, message } from 'antd';
import {
    CaretLeftFilled, BackwardFilled, CaretRightFilled,
    ForwardFilled, SelectOutlined, EyeOutlined
} from '@ant-design/icons';

import { effectHandler } from './util/effect-handler';
import CorrectModal from './operator/index';
import FrameCorrectModal from './frame-correct-modal';

const frameDetail = (record, rowIndex, effectList, type, { state, dispatch, modal }) => {
    let page =
        'first' === type ? state.editor.perfAssess.operator.speedAnalyse.firstPage :
            state.editor.perfAssess.operator.speedAnalyse.lastPage;
    let totalCount = record.frameList.length;
    let frameJsx = [];
    // 标题
    frameJsx.push(
        <li style={{ writingMode: 'vertical-lr', flexDirection: 'column', textAlign: 'center' }}>
            {'first' === type ? '首帧校准' : '尾帧校准'}
        </li>
    );
    // 左翻页
    frameJsx.push(
        <li
            style={{
                margin: '20px 10px', display: 'flex', alignItems: 'center', fontSize: 30,
                visibility: page !== 0 ? 'unset' : 'hidden'
            }}
            key={-1}
        >
            <Space direction="vertical">
                {/* 跳转首页 */}
                <BackwardFilled
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                        let payload = {};
                        if ('first' === type) {
                            payload.firstPage = 0;
                        }
                        else {
                            payload.lastPage = 0;
                        }
                        dispatch({ type: 'editor/setSpeedAnalyse', payload });
                        modal.update({
                            content: (
                                <FrameCorrectModal
                                    record={record}
                                    rowIndex={rowIndex}
                                    dispatch={dispatch}
                                    state={state}
                                    modal={modal}
                                />
                            )
                        });
                    }}
                />
                {/* 跳转上页 */}
                <CaretLeftFilled
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                        let payload = {};
                        if ('first' === type) {
                            payload.firstPage = page - 10 < 0 ? 0 : page - 10;
                        }
                        else {
                            payload.lastPage = page - 10 < 0 ? 0 : page - 10;
                        }
                        dispatch({ type: 'editor/setSpeedAnalyse', payload });
                        modal.update({
                            content: (
                                <FrameCorrectModal
                                    record={record}
                                    rowIndex={rowIndex}
                                    dispatch={dispatch}
                                    state={state}
                                    modal={modal}
                                />
                            )
                        });
                    }}
                />
            </Space>
        </li>
    );
    // 生成每一帧图片
    for (let index = page; index < page + 10; index++) {
        if (index >= totalCount) {
            break;
        }
        let border = '1px solid';
        let maskMsg = '';
        if (record.frameList[index].name) {
            border = '5px solid #2F4ED6';
        }
        switch (type) {
            case 'first':
                if (index === record.smartFirstFrame) {
                    maskMsg = '智能首帧';
                }
                if (index === record.manualFirstFrame) {
                    maskMsg = '人工首帧';
                    border = '5px solid red';
                }
                break;
            case 'last':
                if (index === record.smartLastFrame) {
                    maskMsg = '智能尾帧';
                }
                if (index === record.manualLastFrame) {
                    maskMsg = '人工尾帧';
                    border = '5px solid red';
                }
                break;
            default: break;
        }
        frameJsx.push(
            <li
                style={{ margin: '20px 5px' }}
                key={index}
            >
                <div
                    style={{
                        height: state.editor.screen.height / 3.5,
                        width: (state.editor.screen.width - 270) / 12,
                    }}
                >
                    <Tooltip
                        title={
                            <div>
                                <div>index: {index}</div>
                                <div>{record.frameList[index].name}</div>
                            </div>
                        }
                    >
                        <Image
                            placeholder
                            style={{
                                height: state.editor.screen.height / 3.5,
                                maxWidth: (state.editor.screen.width - 270) / 12,
                                border
                            }}
                            src={record.frameList[index].frame}
                            preview={{
                                mask: (
                                    <Space direction='vertical'>
                                        <Button
                                            shape='round'
                                            icon={<SelectOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                switch (type) {
                                                    case 'first':
                                                        record.manualFirstFrame = index;
                                                        record.manualFirstTimestamp = record.frameList[index].timestamp;
                                                        record.manualFirstStatus = 1;
                                                        break;
                                                    case 'last':
                                                        record.manualLastFrame = index;
                                                        record.manualLastTimestamp = record.frameList[index].timestamp;
                                                        record.manualLastStatus = 1;
                                                        break;
                                                    default: break;
                                                }
                                                let correctList =
                                                    state.editor.perfAssess.operator.speedAnalyse.correctList;
                                                correctList[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        correctList
                                                    }
                                                });
                                                dispatch({
                                                    type: 'editor/speedFrameCorrect',
                                                    payload: {
                                                        recordId: record.recordId,
                                                        type,
                                                        frame: index,
                                                        timestamp: record.frameList[index].timestamp
                                                    }
                                                });
                                                message.success('已校准');
                                                modal.update({
                                                    content: (
                                                        <FrameCorrectModal
                                                            record={record}
                                                            rowIndex={rowIndex}
                                                            dispatch={dispatch}
                                                            state={state}
                                                            modal={modal}
                                                        />
                                                    )
                                                });
                                            }}
                                        >
                                            校准
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<EyeOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                dispatch({
                                                    type: 'editor/setSpeedAnalyse',
                                                    payload: {
                                                        previewIndex: index
                                                    }
                                                });
                                                modal.update({
                                                    content: (
                                                        <FrameCorrectModal
                                                            record={record}
                                                            rowIndex={rowIndex}
                                                            dispatch={dispatch}
                                                            state={state}
                                                            modal={modal}
                                                        />
                                                    )
                                                });
                                            }}
                                        >
                                            预览1
                                        </Button>
                                    </Space>
                                ),
                                visible: state.editor.perfAssess.operator.speedAnalyse.previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) {
                                        dispatch({
                                            type: 'editor/setSpeedAnalyse',
                                            payload: {
                                                previewIndex: -1
                                            }
                                        });
                                        modal.update({
                                            content: (
                                                <FrameCorrectModal
                                                    record={record}
                                                    rowIndex={rowIndex}
                                                    dispatch={dispatch}
                                                    state={state}
                                                    modal={modal}
                                                />
                                            )
                                        });
                                    }
                                }
                            }}
                        />
                    </Tooltip>
                </div>
                <div
                    style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                >
                    <Tag
                        color="orange"
                        style={{ padding: '0 8px' }}
                    >
                        {record.frameList[index].timestamp - record.frameList[0].timestamp} ms
                    </Tag>
                </div>
                {
                    '' !== maskMsg ? (
                        <div
                            style={{
                                textAlign: 'center',
                                zIndex: 998,
                                marginTop: -50,
                                background: 'rgba(0, 0, 0, 0.5)',
                                color: 'white',
                                position: 'relative'
                            }}
                        >
                            {maskMsg}
                        </div>
                    ) : null
                }
            </li>
        );
    }
    // 右翻页按钮
    frameJsx.push(
        <li
            style={{
                margin: '20px 10px', display: 'flex', alignItems: 'center', fontSize: 30,
                visibility: page + 10 < totalCount ? 'unset' : 'hidden'
            }}
            key={-2}
        >
            <Space direction="vertical">
                {/* 跳转尾页 */}
                <ForwardFilled
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                        let payload = {};
                        if ('first' === type) {
                            payload.firstPage = totalCount - 10;
                        }
                        else {
                            payload.lastPage = totalCount - 10;
                        }
                        dispatch({ type: 'editor/setSpeedAnalyse', payload });
                        modal.update({
                            content: (
                                <FrameCorrectModal
                                    record={record}
                                    rowIndex={rowIndex}
                                    dispatch={dispatch}
                                    state={state}
                                    modal={modal}
                                />
                            )
                        });
                    }}
                />
                {/* 跳转后页 */}
                <CaretRightFilled
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                        let payload = {};
                        if ('first' === type) {
                            payload.firstPage = page + 10 >= totalCount ? totalCount - 10 : page + 10;
                        }
                        else {
                            payload.lastPage = page + 10 >= totalCount ? totalCount - 10 : page + 10;
                        }
                        dispatch({ type: 'editor/setSpeedAnalyse', payload });
                        modal.update({
                            content: (
                                <FrameCorrectModal
                                    record={record}
                                    rowIndex={rowIndex}
                                    dispatch={dispatch}
                                    state={state}
                                    modal={modal}
                                />
                            )
                        });
                    }}
                />
            </Space>
        </li>
    );
    return (
        <ul
            style={{
                display: 'inline-flex', flexWrap: 'wrap',
                justifyContent: 'flex-start', listStyle: 'none'
            }}
        >
            {frameJsx}
        </ul>
    );
};

export default ({ record, rowIndex, dispatch, state, modal }) => {
    let effectList = effectHandler(record);
    return (
        <div style={{ height: state.editor.screen.height * 2 / 3.5 + 150 }}>
            <CorrectModal
                record={record}
                rowIndex={rowIndex}
                effectList={effectList}
                currentModal='summary'
                modal={modal}
                state={state}
                dispatch={dispatch}
            />
            <Row>
                {frameDetail(record, rowIndex, effectList, 'first', { state, dispatch, modal })}
                <br />
                {frameDetail(record, rowIndex, effectList, 'last', { state, dispatch, modal })}
            </Row>
        </div>
    );
};
