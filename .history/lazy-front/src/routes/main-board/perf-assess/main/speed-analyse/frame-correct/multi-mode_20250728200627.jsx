/**
 * @file mutil-mode.jsx 多模式转化
 * @create 李爽@2022.09.23
 */
import { Segmented } from 'antd';
import { AppstoreOutlined, BarsOutlined, FormOutlined } from '@ant-design/icons';
import { getMainFrame } from '../../../util/get-frame';

export default ({ dispatch, state }) => {
    const collapsed = () => {
        dispatch({
            type: 'editor/setPerfAssess',
            payload: {
                isPerfPlanCollapsed: true
            }
        });
        if (-1 !== Object.keys(state.editor.perfAssess.operator.speedAnalyse).indexOf('mode') &&
            2 === state.editor.perfAssess.operator.speedAnalyse.mode) {
            getMainFrame(state, dispatch);
        }
    };

    const changeMode = (mode) => {
        dispatch({
            type: 'editor/setSpeedAnalyse',
            payload: {
                mode
            }
        });
    };
    return (
        <div style={{ position: 'absolute', right: 15, top: 10, zIndex: 998 }}>
            <Segmented
                value={state.editor.perfAssess.operator.speedAnalyse.mode}
                options={[
                    {
                        label: '列表模式',
                        value: 1,
                        icon: <BarsOutlined />,
                    },
                    {
                        label: '1',
                        value: 2,
                        icon: <FormOutlined />,
                    },
                    {
                        label: '滑动校准',
                        value: 3,
                        icon: <AppstoreOutlined />,
                    }
                ]}
                onChange={(value) => {
                    switch (value) {
                        case 1:
                            changeMode(1);
                            break;
                        case 2:
                            collapsed();
                            changeMode(2);
                            break;
                        case 3:
                            collapsed();
                            changeMode(3);
                            break;
                        default:
                            break;
                    }
                }}
            />
        </div>
    );
};
