/**
* @file frame-correct.jsx 检查模式弹窗
* @create 李爽@2022.09.26
*/
import { Layout, Image, Tag, Button, Space, Anchor, Tooltip, message } from 'antd';
import { EyeOutlined, PicLeftOutlined, PicRightOutlined } from '@ant-design/icons';
import LazyLoad from 'react-lazyload';

import styles from '../../../../../../../assets/css/video-analyse/check-correct.module.css';

const { Content } = Layout;
const { Link } = Anchor;
const lazyLoadImg = '../../../../../../../../image/loading.png';

const frameDetail = (record, rowIndex, { state, dispatch }) => {
    let totalCount = record.frameList.length;
    let frameJsx = [];
    // 生成每一帧图片
    for (let index = 0; index < totalCount; index++) {
        if (index < 0) {
            continue;
        }
        let border = '1px solid';
        let maskMsg = '';
        let id = '';
        id = `stage_${index}`
        if (undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
            -1 !== state.editor.perfAssess.operator.speedAnalyse.stageList.indexOf(index)) {
            border = '3px dashed orange';
            maskMsg = '转场点';
        }
        if (index === record.smartFirstFrame) {
            border = '3px dashed #ff0000';
            maskMsg = '智能首帧';
            id = 'smart-first-frame';
        } else if (index === record.smartLastFrame) {
            border = '3px dashed #E8A20F';
            maskMsg = '智能尾帧';
            id = 'smart-last-frame';
        }
        if (index === record.manualFirstFrame) {
            border = '3px solid #ff0000';
            maskMsg = '人工首帧';
            id = 'manual-first-frame';
        }
        if (index === record.manualLastFrame) {
            border = '3px solid #E8A20F';
            maskMsg = '人工尾帧';
            id = 'manual-last-frame';
        }
        // 锚点
        frameJsx.push(
            <Tooltip
                placement='top'
                title={
                    <div>
                        <div>index: {index}</div>
                        <div>{record.frameList[index].name}</div>
                    </div>
                }
            >
                <li
                    key={index}
                    className={styles.whole_frame_li}
                    style={{
                        width: state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '11.5%' : '15.66%'
                    }}
                    id={'' !== id ? id : null}
                >
                    <div
                        style={{
                            position: 'absolute',
                            left: 0,
                            zIndex: 998,
                            width: border.slice(0, 4),
                            height: (state.editor.screen.height - 300) / 2,
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            right: 0,
                            zIndex: 998,
                            width: border.slice(0, 4),
                            height: (state.editor.screen.height - 300) / 2,
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            zIndex: 998,
                            width: '100%',
                            height: border.slice(0, 4),
                            border
                        }}>
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            bottom: 27,
                            zIndex: 998,
                            width: '100%',
                            height: border.slice(0, 4),
                            border
                        }}>
                    </div>
                    <LazyLoad
                        resize
                        scrollContainer={document.getElementsByClassName('ulImgs')}
                        overflow
                        placeholder={
                            <img
                                style={{
                                    height: state.editor.screen.height / 3.5,
                                    width: (state.editor.screen.width - 270) / 12,
                                }}
                                src={lazyLoadImg}
                                alt='logo'
                            />
                        }
                    >
                        <Image
                            placeholder
                            width={'100%'}
                            height={(state.editor.screen.height - 300) / 2}
                            src={record.frameList[index].uri}
                            preview={{
                                mask: (
                                    <Space direction='vertical'>
                                        <Button
                                            shape='round'
                                            icon={<PicLeftOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                let payload = { recordId: record.id };
                                                if (0 === record.isManualCorrect) {
                                                    record.manualFirstFrame = record.smartFirstFrame;
                                                    record.manualLastFrame = record.smartLastFrame;
                                                }
                                                payload.manualFirstFrame = index;
                                                record.manualFirstFrame = index;
                                                record.isManualCorrect = 2;
                                                payload.isManualCorrect = 2;
                                                let newData = state.editor.videoAnalyse.operator.videoList;
                                                newData[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setVideoAnalyseOperator',
                                                    payload: {
                                                        videoList: newData
                                                    }
                                                });
                                                dispatch({ type: 'editor/frameManualCorrect', payload });
                                                message.success('首帧已设定');
                                            }}
                                        >
                                            首帧
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<EyeOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                dispatch({
                                                    type: 'editor/setVideoAnalyseOperator',
                                                    payload: {
                                                        previewIndex: index
                                                    }
                                                });
                                            }}
                                        >
                                            预览
                                        </Button>
                                        <Button
                                            shape='round'
                                            icon={<PicRightOutlined />}
                                            style={{ opacity: 0.8 }}
                                            onClick={() => {
                                                let payload = { recordId: record.id };
                                                if (0 === record.isManualCorrect) {
                                                    record.manualFirstFrame = record.smartFirstFrame;
                                                    record.manualLastFrame = record.smartLastFrame;
                                                }
                                                payload.manualLastFrame = index;
                                                record.manualLastFrame = index;
                                                record.isManualCorrect = 2;
                                                payload.isManualCorrect = 2;
                                                let newData = state.editor.videoAnalyse.operator.videoList;
                                                newData[rowIndex] = record;
                                                dispatch({
                                                    type: 'editor/setVideoAnalyseOperator',
                                                    payload: {
                                                        videoList: newData
                                                    }
                                                });
                                                dispatch({ type: 'editor/frameManualCorrect', payload });
                                                message.success('尾帧已设定');
                                            }}
                                        >
                                            尾帧
                                        </Button>
                                    </Space>
                                ),
                                visible: state.editor.videoAnalyse.operator.previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) {
                                        dispatch({
                                            type: 'editor/setVideoAnalyseOperator',
                                            payload: {
                                                previewIndex: -1
                                            }
                                        });
                                    }
                                }
                            }}
                        />
                        {
                            '' !== maskMsg ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                        width: '100%',
                                        zIndex: 998,
                                        marginTop: -22,
                                        background: 'rgba(0, 0, 0, 0.5)',
                                        color: 'white',
                                        position: 'relative'
                                    }}
                                >
                                    {maskMsg}
                                </div>
                            ) : null
                        }

                    </LazyLoad>
                    <div
                        style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                    >
                        <Tag
                            color="orange"
                            style={{ padding: '0 8px' }}
                        >
                            {Math.floor(index * (1000 / record.fps))} ms
                        </Tag>
                    </div>
                </li>
            </Tooltip>
        );
    }

    return frameJsx;
};

export default ({ dispatch, state, rowIndex, record }) => {
    setTimeout(() => {
        if (document.getElementById('manual-first-frame')) {
            document.getElementById('manual-first-frame').scrollIntoView({
                block: 'center',
                inline: 'center'
            });
        }
    }, 100);

    const getLinkJsx = () => {
        let jsx = [];
        let stageList = state.editor.videoAnalyse.operator.stageList;
        for (let item of stageList) {
            jsx.push(<Link href={`#stage_${item}`} title={item} />);
        }
        return jsx;
    }
    return (
        <Content
            id={'correct-list'}
            style={{
                position: 'relative',
                width: '100%',
                overflow: 'scroll',
                height: state.editor.screen.height - 330,
                marginTop: -10,
                backgroundColor: '#fff'
            }}
        >
            <ul
                className='ulImgs'
                style={{
                    listStyle: 'none',
                    width: state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? state.editor.screen.width - 200 : state.editor.screen.width - 460,
                    height: '100%',
                    margin: 0,
                    marginLeft: 100,
                    padding: 0,
                    textAlign: 'center'
                }}
            >
                {
                    0 !== Object.keys(record).length ?
                        frameDetail(record, rowIndex, { state, dispatch }) : null
                }
            </ul>
            {
                0 !== Object.keys(record).length ?
                    <div style={{ width: 100 }}>
                        <h4 style={{ position: 'fixed', marginLeft: 10, top: 250 }}>重要节点导航</h4>
                        <Anchor
                            targetOffset={(state.editor.screen.height - 250) / 2}
                            getContainer={() => document.getElementById('correct-list')}
                            style={{
                                position: 'fixed',
                                marginLeft: 10,
                                top: 280
                            }}
                        >
                            <Link href={'#smart-first-frame'} title='智能首帧' />
                            <Link href={'#smart-last-frame'} title='智能尾帧' />
                            <Link href={'#manual-first-frame'} title='人工首帧' />
                            <Link href={'#manual-last-frame'} title='人工尾帧' />
                        </Anchor>
                        {
                            0 !== state.editor.videoAnalyse.operator.stageList.length ?
                                <h4 style={{ position: 'fixed', marginLeft: 10, top: 415 }}>转场节点导航</h4> : null
                        }
                        {
                            0 !== state.editor.videoAnalyse.operator.stageList.length ?
                                <Anchor
                                    targetOffset={(state.editor.screen.height - 400) / 2}
                                    getContainer={() => document.getElementById('correct-list')}
                                    style={{
                                        position: 'fixed',
                                        marginLeft: 10,
                                        top: 440,
                                        height: 200,
                                        overflow: 'scroll'
                                    }}
                                >
                                    {
                                        getLinkJsx()
                                    }
                                </Anchor> : null
                        }
                    </div>
                    : null
            }
        </Content>
    );
};
