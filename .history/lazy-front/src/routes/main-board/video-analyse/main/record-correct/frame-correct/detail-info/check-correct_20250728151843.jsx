/**
* @file check-coorect.jsx 检查模式弹窗
* @create 王家麒@2022.04.29
*/
import React from 'react';
import { Image, Tag, Button, Layout, Tooltip, message } from 'antd';
import { EyeOutlined, RotateLeftOutlined, CaretLeftFilled, RotateRightOutlined, CaretRightFilled } from '@ant-design/icons';

import { keyDown } from '../../../../util/key-down';
import styles from '../../../../../../../assets/css/video-analyse/check-correct.module.css';
import BarSlider from '../component/bar-slider';
const loadImg = '../../../../../../../../image/img.png';
const { Content } = Layout;
// const { Link } = Anchor;

const frameDetail = (record, rowIndex, type, { state, dispatch }) => {
    let page = 'first' === type ? state.editor.videoAnalyse.operator.firstPage :
        state.editor.videoAnalyse.operator.lastPage;

    let totalCount = record.frameList.length;
    let pageNum = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 7 : 5;
    let frameJsx = [];

    // 生成每一帧图片
    for (let index = page; index < page + pageNum; index++) {
        if (index >= totalCount) {
            break;
        }
        let border = '1px solid';
        let maskMsg = '';
        if (undefined !== state.editor.perfAssess.operator.speedAnalyse.stageList &&
            -1 !== state.editor.videoAnalyse.operator.stageList.indexOf(index)) {
            border = '3px dashed orange';
            maskMsg = '转场点';
        }
        if ('first' === type) {
            if (index === record.smartFirstFrame) {
                border = '3px dashed #ff0000';
                maskMsg = '智能首帧';
            }
            if (index === record.manualFirstFrame) {
                border = '3px solid #ff0000';
                maskMsg = '人工首帧';
            }
        }
        else {
            if (index === record.smartLastFrame) {
                border = '3px dashed #ff0000';
                maskMsg = '智能尾帧';
            }
            if (index === record.manualLastFrame) {
                border = '3px solid #ff0000';
                maskMsg = '人工尾帧';
            }
        }
        frameJsx.push(
            <li
                id={`${type}_stage_${index}`}
                key={index}
                className={styles.frame_li}
                style={{
                    width: state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '13.428%' : '19.2%'
                }}
            >
                <div
                    style={{
                        position: 'absolute',
                        left: 0,
                        zIndex: 998,
                        width: border.slice(0, 4),
                        height: (state.editor.screen.height - 310) / 2,
                        border
                    }}>
                </div>
                <div
                    style={{
                        position: 'absolute',
                        right: 0,
                        zIndex: 998,
                        width: border.slice(0, 4),
                        height: (state.editor.screen.height - 310) / 2,
                        border
                    }}>
                </div>
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        zIndex: 998,
                        width: '100%',
                        height: border.slice(0, 4),
                        border
                    }}>
                </div>
                <div
                    style={{
                        position: 'absolute',
                        bottom: 27,
                        zIndex: 998,
                        width: '100%',
                        height: border.slice(0, 4),
                        border
                    }}>
                </div>
                <Image
                    placeholder
                    width={'100%'}
                    height={(state.editor.screen.height - 310) / 2}
                    src={record.frameList[index].uri ? record.frameList[index].uri : loadImg}
                    preview={{
                        mask: (
                            <Button
                                shape='round'
                                icon={<EyeOutlined />}
                                style={{
                                    position: 'absolute',
                                    bottom: 35
                                }}
                                onClick={() => {
                                    dispatch({
                                        type: 'editor/setVideoAnalyseOperator',
                                        payload: {
                                            previewIndex: index
                                        }
                                    });
                                }}
                            >
                                预览
                            </Button>
                        ),
                        visible: state.editor.videoAnalyse.operator.previewIndex === index,
                        onVisibleChange: (visible) => {
                            if (!visible) {
                                dispatch({
                                    type: 'editor/setVideoAnalyseOperator',
                                    payload: {
                                        previewIndex: -1
                                    }
                                });
                            }
                        }
                    }}
                    onClick={() => {
                        dispatch({
                            type: 'editor/setVideoAnalyseOperator',
                            payload: {
                                isFrame: 'first' === type ? 1 : 2
                            }
                        });
                    }}
                    onDoubleClick={() => {
                        let payload = { recordId: record.id };
                        if (0 === record.isManualValid) {
                            if ('first' === type) {
                                payload.manualFirstFrame = index;
                                record.manualFirstFrame = index;
                            } else {
                                payload.manualLastFrame = index;
                                record.manualLastFrame = index;
                            }
                            record.isManualCorrect = 2;
                            payload.isManualCorrect = 2;
                            let newData = state.editor.videoAnalyse.operator.videoList;
                            newData[rowIndex] = record;
                            dispatch({
                                type: 'editor/setVideoAnalyseOperator',
                                payload: {
                                    videoList: newData
                                }
                            });
                            dispatch({ type: 'editor/frameManualCorrect', payload });
                            dispatch({ type: 'editor/getPlanStatistic' });
                        } else {
                            message.info('该组分帧图已废弃');
                        }
                    }}
                />

                {
                    '' !== maskMsg ? (
                        <div
                            style={{
                                textAlign: 'center',
                                width: '100%',
                                zIndex: 998,
                                marginTop: -22,
                                background: 'rgba(0, 0, 0, 0.5)',
                                color: 'white',
                                position: 'relative'
                            }}
                        >
                            {maskMsg}
                        </div>
                    ) : null
                }
                <div
                    style={{ margin: '5px 0 0 0', textAlign: 'center' }}
                >
                    <Tag
                        color="green"
                        style={{ padding: '0 8px' }}
                    >
                        index {index}
                    </Tag>
                    <Tag
                        color="orange"
                        style={{ padding: '0 8px' }}
                    >
                        {Math.floor(index * (1000 / record.fps))} ms
                    </Tag>
                </div>
            </li>
        );
    }

    return frameJsx;
};

const left = (state, dispatch, type) => {
    let page = 'first' === type ? state.editor.videoAnalyse.operator.firstPage :
        state.editor.videoAnalyse.operator.lastPage;
    let pageNum = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 3 : 2;
    return (
        <div
            style={{
                position: 'relative',
                width: '3%',
                float: 'left',
                height: (state.editor.screen.height - 310) / 2
            }}
        >
            {/* 跳转上一个趋势点 */}
            {
                undefined !== state.editor.videoAnalyse.operator.stageList && 0 !== state.editor.videoAnalyse.operator.stageList.length ?
                    <Tooltip title={'跳转上一个转场趋势点'}>
                        <RotateLeftOutlined
                            style={{
                                position: 'absolute',
                                top: '40%',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                let payload = {};
                                let changePage = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 3 : 2;
                                if ('first' === type) {
                                    let stageList = state.editor.videoAnalyse.operator.stageList;
                                    let node = 0;
                                    for (let [index, item] of stageList.entries()) {
                                        if (item > state.editor.videoAnalyse.operator.firstPage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (state.editor.videoAnalyse.operator.firstPage > stageList[stageList.length - 1]) {
                                        dispatch({
                                            type: 'editor/setVideoAnalyseOperator',
                                            payload: {
                                                firstPage: stageList[stageList.length - 1] > changePage ?
                                                    stageList[stageList.length - 1] - changePage : 0,
                                                firstNode: stageList.length - 1
                                            }
                                        });
                                    } else if (node > 0) {
                                        payload.firstPage = stageList[node - 1] > changePage ? stageList[node - 1] - changePage : stageList[node - 1];
                                        payload.firstNode = node - 1;
                                        payload.isFrame = 1;
                                    } else {
                                        message.info('已跳转至第一个转场趋势点')
                                    }
                                } else {
                                    let stageList = state.editor.videoAnalyse.operator.stageList;
                                    let node = 0;
                                    for (let [index, item] of stageList.entries()) {
                                        if (item > state.editor.videoAnalyse.operator.lastPage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (state.editor.videoAnalyse.operator.lastPage > stageList[stageList.length - 1]) {
                                        dispatch({
                                            type: 'editor/setVideoAnalyseOperator',
                                            payload: {
                                                lastPage: stageList[stageList.length - 1] > changePage ?
                                                    stageList[stageList.length - 1] - changePage : 0,
                                                lastNode: stageList.length - 1
                                            }
                                        });
                                    } else if (node > 0) {
                                        payload.lastPage = stageList[node - 1] > changePage ? stageList[node - 1] - changePage : stageList[node - 1];
                                        payload.lastNode = node - 1;
                                        payload.isFrame = 2;
                                    } else {
                                        message.info('已跳转至第一个转场趋势点')
                                    }
                                }
                                dispatch({ type: 'editor/setVideoAnalyseOperator', payload });
                            }}
                        />
                    </Tooltip> : null
            }
            {/* 跳转上页 */}
            <Tooltip title={'跳转上一页'}>
                <CaretLeftFilled
                    style={{
                        position: 'absolute',
                        bottom: undefined !== state.editor.videoAnalyse.operator.stageList &&
                            0 !== state.editor.videoAnalyse.operator.stageList.length ? '40%' : '50%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        let payload = {};
                        if ('first' === type) {
                            payload.firstPage = page - pageNum < 0 ? 0 : page - pageNum;
                            payload.isFrame = 1;
                        }
                        else {
                            payload.lastPage = page - pageNum < 0 ? 0 : page - pageNum;
                            payload.isFrame = 2;
                        }
                        dispatch({ type: 'editor/setVideoAnalyseOperator', payload });
                    }}
                />
            </Tooltip>
        </div>
    );
}

const right = (state, dispatch, record, type) => {
    let page = 'first' === type ? state.editor.videoAnalyse.operator.firstPage :
        state.editor.videoAnalyse.operator.lastPage;
    let totalCount = record.frameList.length;
    let pageNum = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 3 : 2;
    return (
        <div
            style={{
                position: 'relative',
                width: '3%',
                float: 'left',
                height: (state.editor.screen.height - 310) / 2
            }}
        >
            {/* 跳转下一个转场趋势点 */}
            {
                undefined !== state.editor.videoAnalyse.operator.stageList && 0 !== state.editor.videoAnalyse.operator.stageList.length ?
                    <Tooltip title={'跳转下一个转场趋势点'}>
                        <RotateRightOutlined
                            style={{
                                position: 'absolute',
                                top: '40%',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                                cursor: 'pointer'
                            }}
                            onClick={() => {
                                let changePage = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 3 : 2;
                                let payload = {};
                                if ('first' === type) {
                                    let stageList = state.editor.videoAnalyse.operator.stageList;
                                    let node = stageList.length - 1;
                                    for (let [index, item] of stageList.entries()) {
                                        if (index !== stageList.length - 1 && item > state.editor.videoAnalyse.operator.firstPage + changePage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (node <= stageList.length - 1) {
                                        payload.firstPage = stageList[node] > changePage ? stageList[node] - changePage : stageList[node];
                                        payload.firstNode = node;
                                        payload.isFrame = 1;
                                        if (node === stageList.length - 1) {
                                            message.info('已跳转至最后一个转场趋势点');
                                        }
                                    }
                                }
                                else {
                                    let stageList = state.editor.videoAnalyse.operator.stageList;
                                    let node = stageList.length - 1;
                                    for (let [index, item] of stageList.entries()) {
                                        if (index !== stageList.length - 1 && item > state.editor.videoAnalyse.operator.lastPage + changePage) {
                                            node = index;
                                            break;
                                        }
                                    }
                                    if (node <= stageList.length - 1) {
                                        payload.lastPage = stageList[node] > changePage ? stageList[node] - changePage : stageList[node];
                                        payload.lastNode = node;
                                        payload.isFrame = 2;
                                        if (node === stageList.length - 1) {
                                            message.info('已跳转至最后一个转场趋势点');
                                        }
                                    }
                                }
                                dispatch({ type: 'editor/setVideoAnalyseOperator', payload });
                            }}
                        />
                    </Tooltip> : null
            }
            {/* 跳转后页 */}
            <CaretRightFilled
                style={{
                    position: 'absolute',
                    bottom: undefined !== state.editor.videoAnalyse.operator.stageList && 0 !== state.editor.videoAnalyse.operator.stageList.length ? '40%' : '50%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    fontSize: state.editor.screen.width <= 1440 ? 20 : 30,
                    cursor: 'pointer'
                }}
                onClick={() => {
                    let payload = {};
                    if ('first' === type) {
                        payload.firstPage = page + pageNum >= totalCount ? totalCount - pageNum : page + pageNum;
                        payload.isFrame = 1;
                    }
                    else {
                        payload.lastPage = page + pageNum >= totalCount ? totalCount - pageNum : page + pageNum;
                        payload.isFrame = 2;
                    }
                    dispatch({ type: 'editor/setVideoAnalyseOperator', payload });
                }}
            />
        </div>
    );
}

export default ({ dispatch, state, rowIndex, record }) => {
    /**
    * 捕获键盘按下，满足校准页面切换
    */
    const onKeyDown = (e) => {
        keyDown(state, dispatch, e, record);
    };
    React.useEffect(() => {
        window.addEventListener('keydown', onKeyDown); // 添加全局事件
        return () => {
            window.removeEventListener('keydown', onKeyDown); // 销毁
        };
    }, []);

    return (
        <Content
            style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}
        >
            {
                undefined !== record && 0 !== record.length ?
                    <div
                        style={{
                            position: 'relative',
                            width: state.editor.screen.width <= 1440 || state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '100%' : '80%',
                            marginLeft: state.editor.screen.width <= 1440 || state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '0%' : '10%',
                            marginTop: 10,
                            height: (state.editor.screen.height - 250) / 2
                        }}
                    >
                        {left(state, dispatch, 'first')}
                        <div style={{ width: '94%', float: 'left' }}>
                            <ul
                                id='first-check-list'
                                style={{
                                    width: '100%',
                                    padding: 0,
                                    margin: 0,
                                    opacity: 2 === state.editor.videoAnalyse.operator.isFrame ? 0.3 : 1,
                                    display: 'inline-flex', flexWrap: 'wrap',
                                    justifyContent: 'flex-start', listStyle: 'none'
                                }}
                            >
                                {frameDetail(record, rowIndex, 'first', { state, dispatch })}
                            </ul>
                        </div>
                        {right(state, dispatch, record, 'first')}
                    </div>
                    : null
            }
            {
                undefined !== record && 0 !== record.length ?
                    <BarSlider state={state} dispatch={dispatch} record={record} type={'first'} /> : null
            }
            {
                undefined !== record && 0 !== record.length ?
                    <div
                        style={{
                            position: 'relative',
                            width: state.editor.screen.width <= 1440 || state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '100%' : '80%',
                            marginLeft: state.editor.screen.width <= 1440 || state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? '0%' : '10%',
                            marginTop: 3,
                            height: (state.editor.screen.height - 250) / 2
                        }}
                    >
                        {left(state, dispatch, 'last')}
                        <div style={{ width: '94%', float: 'left' }}>
                            <ul
                                id='last-check-list'
                                style={{
                                    width: '100%',
                                    padding: 0,
                                    margin: 0,
                                    opacity: 1 === state.editor.videoAnalyse.operator.isFrame ? 0.2 : 1,
                                    display: 'inline-flex', flexWrap: 'wrap',
                                    justifyContent: 'flex-start', listStyle: 'none'
                                }}
                            >
                                {frameDetail(record, rowIndex, 'last', { state, dispatch })}
                            </ul>
                        </div>
                        {right(state, dispatch, record, 'last')}
                    </div> : null
            }
            {
                undefined !== record && 0 !== record.length ?
                    <BarSlider state={state} dispatch={dispatch} record={record} type={'last'} /> : null
            }
        </Content>
    );
};
