/**
 * @file mutil-mode.jsx 多模式转化
 * @create 李爽@2022.09.23
 */
import { Segmented } from 'antd';
import { AppstoreOutlined, BarsOutlined, FormOutlined } from '@ant-design/icons';

export default ({ dispatch, state }) => {
    const getPage = () => {
        // 确定主显示帧
        let changePage = state.editor.videoAnalyse.operator.isVideoPlanCollapsed ? 3 : 2;
        let { videoList, correctTaskIndex } = state.editor.videoAnalyse.operator;
        if (0 !== videoList.length) {
            let record = videoList[correctTaskIndex];
            let firstIndex = 0 === record.isManualCorrect ? record.smartFirstFrame : record.manualFirstFrame;
            let lastIndex = 0 === record.isManualCorrect ? record.smartLastFrame : record.manualLastFrame;
            dispatch({
                type: 'editor/setVideoAnalyseOperator',
                payload: {
                    firstPage: firstIndex - changePage < 0 ? 0 : firstIndex - changePage,
                    lastPage: lastIndex - changePage < 0 ? 0 : lastIndex - changePage
                }
            });
        }
    };
    return (
        <div style={{ position: 'absolute', right: 15, top: 10, zIndex: 998 }}>
            <Segmented
                value={state.editor.videoAnalyse.operator.mode}
                options={[
                    {
                        label: '列表模式',
                        value: 1,
                        icon: <BarsOutlined />,
                    },
                    {
                        label: '分页校准',
                        value: 2,
                        icon: <FormOutlined />,
                    },
                    {
                        label: '滑动校准',
                        value: 3,
                        icon: <AppstoreOutlined />,
                    }
                ]}
                onChange={(value) => {
                    switch (value) {
                        case 1:
                            dispatch({
                                type: 'editor/setVideoAnalyseOperator',
                                payload: {
                                    mode: 1
                                }
                            });
                            break;
                        case 2:
                            // 确定主显示帧
                            getPage();
                            dispatch({
                                type: 'editor/setVideoAnalyseOperator',
                                payload: {
                                    mode: 2
                                }
                            });
                            break;
                        case 3:
                            dispatch({
                                type: 'editor/setVideoAnalyseOperator',
                                payload: {
                                    mode: 3
                                }
                            });
                            break;
                        default:
                            break;
                    }
                }}
            />
        </div>
    );
};
